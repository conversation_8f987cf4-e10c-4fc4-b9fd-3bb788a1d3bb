import { fabric } from "fabric";
import { gifToSprite } from "./gifToSprite";
import "../fabric-utils"; // 确保CoverElementBase已加载

const [PLAY, PAUSE, STOP] = [0, 1, 2];

interface GifResult {
  error?: string;
  play?: () => void;
  pause?: () => void;
  stop?: () => void;
  getStatus?: () => string;
}

interface AnimatedGifImage extends fabric.Image {
  play: () => void;
  pause: () => void;
  stop: () => void;
  getStatus: () => string;
  frameWidth: number;
  frameHeight: number;
  framesLength: number;
  delay: number;
  animationId?: number;
  updateFrame?: (currentTime: number) => boolean;
  destroy?: () => void;
  // 继承CoverElementBase的边框和剪切属性
  imageBorderColor?: string;
  borderWidth?: number;
  borderStyle?: string;
  borderRadius?: number;
  cropX?: number;
  cropY?: number;
  cropWidth?: number;
  cropHeight?: number;
  disableCrop?: boolean;
  setBorder?: (color: string, width: number, style?: string) => void;
  _renderBorder?: (ctx: CanvasRenderingContext2D, path: Path2D) => void;
  _createClipPath?: () => Path2D;
}

// 全局动画管理器，统一管理所有GIF动画
class GifAnimationManager {
  private static instance: GifAnimationManager;
  private animatedGifs = new Set<AnimatedGifImage>();
  private animationFrameId: number | null = null;
  private lastFrameTime = 0;

  static getInstance(): GifAnimationManager {
    if (!GifAnimationManager.instance) {
      GifAnimationManager.instance = new GifAnimationManager();
    }
    return GifAnimationManager.instance;
  }

  addGif(gif: AnimatedGifImage) {
    this.animatedGifs.add(gif);
    this.startAnimation();
  }

  removeGif(gif: AnimatedGifImage) {
    this.animatedGifs.delete(gif);
    if (this.animatedGifs.size === 0) {
      this.stopAnimation();
    }
  }

  private startAnimation() {
    if (this.animationFrameId) return;

    const animate = (currentTime: number) => {
      // 使用时间间隔控制帧率，避免过度渲染
      if (currentTime - this.lastFrameTime >= 16) {
        // 约60fps
        this.updateGifs(currentTime);
        this.lastFrameTime = currentTime;
      }

      if (this.animatedGifs.size > 0) {
        this.animationFrameId = requestAnimationFrame(animate);
      }
    };

    this.animationFrameId = requestAnimationFrame(animate);
  }

  private stopAnimation() {
    if (this.animationFrameId) {
      cancelAnimationFrame(this.animationFrameId);
      this.animationFrameId = null;
    }
  }

  private updateGifs(currentTime: number) {
    let needsRender = false;

    this.animatedGifs.forEach((gif) => {
      if (
        gif.getStatus() === "Playing" &&
        gif.updateFrame &&
        gif.updateFrame(currentTime)
      ) {
        needsRender = true;
      }
    });

    // 批量渲染，减少重绘次数
    if (needsRender) {
      this.animatedGifs.forEach((gif) => {
        if (gif.canvas) {
          gif.dirty = true;
        }
      });
    }
  }

  cleanup() {
    this.stopAnimation();
    this.animatedGifs.clear();
  }
}

/**
 * 优化的fabricGif函数
 * @param gif 可以是URL、dataURL或File对象
 * @param maxWidth 可选，缩放到最大宽度
 * @param maxHeight 可选，缩放到最大高度
 * @param maxDuration 可选，以毫秒为单位减少GIF帧到最大持续时间
 * @returns 错误对象或带有播放控制方法的fabric.Image实例
 */
export const fabricGif = async (
  gif: string | File,
  maxWidth?: number,
  maxHeight?: number,
  maxDuration?: number
): Promise<AnimatedGifImage | GifResult> => {
  try {
    const { error, dataUrl, delay, frameWidth, framesLength } =
      await gifToSprite(gif, maxWidth, maxHeight, maxDuration);

    if (error) return { error } as any;

    return new Promise((resolve, reject) => {
      fabric.Image.fromURL(dataUrl, (img: fabric.Image) => {
        if (!img) {
          reject(new Error("Failed to create fabric image"));
          return;
        }

        const sprite = img.getElement() as HTMLImageElement;
        let framesIndex = 0;
        let lastFrameTime = 0;
        let status: number = PAUSE;

        // 将普通的fabric.Image转换为具有CoverElementBase功能的对象
        const animatedGif = img as AnimatedGifImage;

        // 添加CoverElementBase的默认属性
        animatedGif.imageBorderColor = "transparent";
        animatedGif.borderWidth = 0;
        animatedGif.borderStyle = "solid";
        animatedGif.borderRadius = 0;
        animatedGif.disableCrop = false;

        // 添加边框设置方法
        animatedGif.setBorder = function (
          color: string,
          width: number,
          style: string = "solid"
        ) {
          if (!color) {
            console.error("Invalid border color");
            return;
          }
          this.imageBorderColor = color;
          this.borderWidth = width;
          this.borderStyle = style;
          this.canvas?.requestRenderAll();
        };

        // 添加边框渲染方法
        animatedGif._renderBorder = function (
          ctx: CanvasRenderingContext2D,
          path: Path2D
        ) {
          if (this.borderWidth <= 0) return;

          ctx.strokeStyle = this.imageBorderColor;
          ctx.lineWidth = this.borderWidth;

          const dashArray =
            this.borderStyle === "dashed"
              ? [this.borderWidth * 2, this.borderWidth]
              : this.borderStyle === "dotted"
              ? [this.borderWidth, this.borderWidth]
              : [];

          ctx.setLineDash(dashArray);
          ctx.stroke(path);
          ctx.setLineDash([]);
        };

        // 添加剪切路径创建方法
        animatedGif._createClipPath = function () {
          const { width, height } = this;
          const r = this.borderRadius;

          const path = new Path2D();
          if (r > 0) {
            // 创建圆角矩形路径
            const x = -width / 2;
            const y = -height / 2;
            path.moveTo(x + r, y);
            path.lineTo(x + width - r, y);
            path.quadraticCurveTo(x + width, y, x + width, y + r);
            path.lineTo(x + width, y + height - r);
            path.quadraticCurveTo(
              x + width,
              y + height,
              x + width - r,
              y + height
            );
            path.lineTo(x + r, y + height);
            path.quadraticCurveTo(x, y + height, x, y + height - r);
            path.lineTo(x, y + r);
            path.quadraticCurveTo(x, y, x + r, y);
          } else {
            // 创建普通矩形路径
            path.rect(-width / 2, -height / 2, width, height);
          }
          return path;
        };

        // 保存帧信息
        animatedGif.frameWidth = frameWidth;
        animatedGif.frameHeight = sprite.naturalHeight;
        animatedGif.framesLength = framesLength;
        animatedGif.delay = delay;

        // 初始化滤镜属性
        (animatedGif as any).brightness = 0;
        (animatedGif as any).contrast = 0;
        (animatedGif as any).saturation = 0;
        (animatedGif as any).hue = 0;
        (animatedGif as any).blur = 0;
        (animatedGif as any).customFilter = "none";

        // 优化的帧更新函数
        animatedGif.updateFrame = function (currentTime: number): boolean {
          if (status !== PLAY) return false;

          if (currentTime - lastFrameTime >= delay) {
            lastFrameTime = currentTime;
            framesIndex = (framesIndex + 1) % framesLength;
            return true; // 需要重绘
          }
          return false;
        };

        // 新增：根据时间轴位置设置GIF帧的函数
        (animatedGif as any).setFrameByTime = function (
          timelineTime: number,
          elementStartTime: number
        ): boolean {
          if (framesLength <= 1) return false;

          // 计算相对于元素开始时间的时间偏移（毫秒）
          const relativeTime = Math.max(0, timelineTime - elementStartTime);

          // 计算应该显示的帧索引
          // 假设GIF以其原始帧率循环播放
          const cycleTime = framesLength * delay; // 一个完整循环的时间
          const timeInCycle = relativeTime % cycleTime; // 当前循环中的时间
          const targetFrameIndex =
            Math.floor(timeInCycle / delay) % framesLength;

          // 如果帧索引发生变化，更新并返回true表示需要重绘
          if (targetFrameIndex !== framesIndex) {
            framesIndex = targetFrameIndex;
            // 标记对象为脏，强制重绘
            this.dirty = true;
            return true;
          }
          return false;
        };

        // 优化的渲染函数，支持滤镜效果、边框和剪切
        animatedGif._render = function (ctx: CanvasRenderingContext2D) {
          ctx.save();

          // 创建剪切路径
          const clipPath = this._createClipPath();

          // 应用剪切路径
          if (clipPath) {
            ctx.clip(clipPath);
          }

          // 应用CSS滤镜
          const filters = [
            `brightness(${100 + (this.brightness || 0)}%)`,
            `contrast(${100 + (this.contrast || 0)}%)`,
            `saturate(${100 + (this.saturation || 0)}%)`,
            `hue-rotate(${this.hue || 0}deg)`,
            `blur(${this.blur || 0}px)`,
          ];

          // 添加效果类型滤镜
          const effectType = this.customFilter;
          if (effectType && effectType !== "none") {
            switch (effectType) {
              case "blackAndWhite":
                filters.push("grayscale(100%)");
                break;
              case "sepia":
                filters.push("sepia(100%)");
                break;
              case "invert":
                filters.push("invert(100%)");
                break;
              case "saturate":
                filters.push("saturate(200%)");
                break;
              case "retro":
                filters.push("sepia(50%) contrast(120%) brightness(110%)");
                break;
            }
          }

          const filterString = filters
            .filter((f) => !f.includes("NaN"))
            .join(" ");

          if (
            filterString &&
            filterString !==
              "brightness(100%) contrast(100%) saturate(100%) hue-rotate(0deg) blur(0px)"
          ) {
            ctx.filter = filterString;
          }

          // 处理剪切功能
          if (
            !this.disableCrop &&
            this.cropX !== undefined &&
            this.cropY !== undefined &&
            this.cropWidth !== undefined &&
            this.cropHeight !== undefined
          ) {
            // 使用剪切参数绘制
            ctx.drawImage(
              sprite,
              frameWidth * framesIndex + (this.cropX || 0),
              this.cropY || 0,
              this.cropWidth,
              this.cropHeight,
              -this.width / 2,
              -this.height / 2,
              this.width,
              this.height
            );
          } else {
            // 始终绘制当前帧索引对应的帧
            // 这样在暂停状态下也能显示正确的帧
            ctx.drawImage(
              sprite,
              frameWidth * framesIndex,
              0,
              frameWidth,
              sprite.height,
              -this.width / 2,
              -this.height / 2,
              this.width,
              this.height
            );
          }

          // 重置滤镜
          ctx.filter = "none";

          // 渲染边框
          if (this._renderBorder && clipPath) {
            this._renderBorder(ctx, clipPath);
          }

          ctx.restore();
        };

        // 播放控制方法
        animatedGif.play = function () {
          if (status !== PLAY) {
            status = PLAY;
            lastFrameTime = performance.now();
            GifAnimationManager.getInstance().addGif(this);
            this.dirty = true;
          }
        };

        animatedGif.pause = function () {
          if (status === PLAY) {
            status = PAUSE;
            GifAnimationManager.getInstance().removeGif(this);
            this.dirty = false;
          }
        };

        animatedGif.stop = function () {
          status = STOP;
          framesIndex = 0;
          GifAnimationManager.getInstance().removeGif(this);
          this.dirty = true;
        };

        animatedGif.getStatus = () => ["Playing", "Paused", "Stopped"][status];

        // 重写销毁方法，确保清理资源
        const originalDestroy = animatedGif.destroy;
        animatedGif.destroy = function () {
          GifAnimationManager.getInstance().removeGif(this);
          if (originalDestroy) {
            originalDestroy.call(this);
          }
        };

        // 默认暂停状态，只有在时间轴播放或scrubbing时才播放
        // animatedGif.play(); // 移除默认播放
        resolve(animatedGif);
      });
    });
  } catch (error) {
    console.error("fabricGif error:", error);
    return { error: error.message || "Unknown error" };
  }
};

// 清理函数
export const cleanupGifAnimations = () => {
  GifAnimationManager.getInstance().cleanup();
};

// 获取当前活动的GIF数量（用于调试）
export const getActiveGifCount = () => {
  return GifAnimationManager.getInstance()["animatedGifs"].size;
};
